#!/usr/bin/env python3
"""
Installation script for RL Portfolio Rebalancing System dependencies - Google Colab Optimized

This script automatically installs all required dependencies for the RL Portfolio 
Rebalancing System, optimized specifically for Google Colab CPU environments.

USAGE:
    python install_dependencies.py [--upgrade] [--verbose] [--dry-run] [--colab]

OPTIONS:
    --upgrade    Upgrade existing packages to latest versions
    --verbose    Show detailed installation output
    --dry-run    Show what would be installed without actually installing
    --colab      Enable Google Colab optimizations (auto-detected)

GOOGLE COLAB OPTIMIZATIONS:
    - CPU-optimized package versions
    - Colab-specific dependency handling
    - Memory-efficient installation order
    - Pre-installed package detection

REQUIREMENTS:
    - Python 3.8 or higher (<PERSON><PERSON> provides 3.10+)
    - pip package manager (pre-installed in Colab)
    - Internet connection for downloading packages

EXIT CODES:
    0 - Success
    1 - General error
    2 - Python version incompatible
    3 - pip not available
    4 - Critical package installation failed
    5 - TensorTrade installation failed
"""

import subprocess
import sys
import os
import argparse
import platform
from typing import List, <PERSON><PERSON>, Optional
from dataclasses import dataclass

# Minimum Python version required
MIN_PYTHON_VERSION = (3, 8)

# Google Colab detection
def is_google_colab() -> bool:
    """Detect if running in Google Colab environment."""
    try:
        import google.colab
        return True
    except ImportError:
        return 'COLAB_GPU' in os.environ or 'COLAB_TPU_ADDR' in os.environ

@dataclass
class InstallationResult:
    """Result of package installation attempt."""
    success: bool
    package: str
    error_message: str = ""
    warning_message: str = ""

class DependencyInstaller:
    """
    Comprehensive dependency installer for RL Portfolio Rebalancing System.
    
    Handles installation of all required packages with proper error handling,
    version checking, and Google Colab optimizations.
    """
    
    def __init__(self, upgrade: bool = False, verbose: bool = False, dry_run: bool = False, colab_mode: bool = None):
        """
        Initialize the dependency installer.
        
        Args:
            upgrade: Whether to upgrade existing packages
            verbose: Whether to show detailed output
            dry_run: Whether to simulate installation without actually installing
            colab_mode: Force Colab mode (auto-detected if None)
        """
        self.upgrade = upgrade
        self.verbose = verbose
        self.dry_run = dry_run
        self.colab_mode = colab_mode if colab_mode is not None else is_google_colab()
        self.results: List[InstallationResult] = []
        
        # Google Colab pre-installed packages (skip these)
        self.colab_preinstalled = {
            "pandas", "numpy", "matplotlib", "seaborn", "scipy", "scikit-learn"
        } if self.colab_mode else set()
        
        # Core packages optimized for Colab CPU
        if self.colab_mode:
            self.core_packages = [
                # Only install if not pre-installed in Colab
                "yfinance>=0.2.59",
                "ta>=0.10.0", 
                "stable-baselines3[extra]>=2.6.0",  # CPU-optimized version
                "gymnasium>=0.29.0",  # Updated gym replacement
                "psutil>=5.9.0",
                # Colab-specific optimizations
                "torch>=2.0.0+cpu",  # CPU-only PyTorch for stable-baselines3
                "tensorboard>=2.13.0",  # For training monitoring
            ]
        else:
            # Standard packages for non-Colab environments
            self.core_packages = [
                "pandas>=2.2.0",
                "numpy>=1.26.0", 
                "yfinance>=0.2.59",
                "ta>=0.10.0",
                "stable-baselines3>=2.6.0",
                "gym>=0.26.0",
                "matplotlib>=3.5.0",
                "seaborn>=0.13.0",
                "psutil>=5.9.0",
                "scikit-learn>=1.1.0",
                "scipy>=1.9.0"
            ]
        
        # TensorTrade installation (from GitHub as per tech.md requirements)
        self.tensortrade_url = "git+https://github.com/tensortrade-org/tensortrade.git"
        
        # Platform-specific packages
        self.platform_packages = self._get_platform_packages()
    
    def _get_platform_packages(self) -> List[str]:
        """Get platform-specific packages if needed."""
        packages = []
        
        if self.colab_mode:
            # Google Colab specific packages
            packages.extend([
                "ipywidgets>=8.0.0",  # For interactive widgets
                "tqdm>=4.64.0",  # Progress bars (may need update)
            ])
        elif platform.system() == "Windows":
            # Windows-specific packages for better Unicode support
            packages.extend([
                "colorama>=0.4.0",  # Better Windows console colors
            ])
        
        return packages
    
    def check_python_version(self) -> bool:
        """
        Check if Python version meets minimum requirements.
        
        Returns:
            True if Python version is compatible, False otherwise
        """
        current_version = sys.version_info[:2]
        if current_version < MIN_PYTHON_VERSION:
            print(f"❌ Python {MIN_PYTHON_VERSION[0]}.{MIN_PYTHON_VERSION[1]}+ required, "
                  f"but {current_version[0]}.{current_version[1]} found")
            if self.colab_mode:
                print("⚠️  Google Colab should provide Python 3.10+. Please restart runtime if needed.")
            else:
                print(f"Please upgrade Python to version {MIN_PYTHON_VERSION[0]}.{MIN_PYTHON_VERSION[1]} or higher")
            return False
        
        colab_info = " (Google Colab)" if self.colab_mode else ""
        print(f"✅ Python {current_version[0]}.{current_version[1]}{colab_info} - Compatible")
        return True
    
    def check_pip_availability(self) -> bool:
        """
        Check if pip is available and working.
        
        Returns:
            True if pip is available, False otherwise
        """
        try:
            result = subprocess.run([sys.executable, "-m", "pip", "--version"], 
                                  capture_output=True, text=True, check=True)
            if self.verbose:
                print(f"✅ pip available: {result.stdout.strip()}")
            else:
                print("✅ pip - Available")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ pip not available")
            print("Please install pip first:")
            print("  • Download get-pip.py from https://bootstrap.pypa.io/get-pip.py")
            print("  • Run: python get-pip.py")
            return False
    
    def check_package_installed(self, package_name: str) -> bool:
        """
        Check if a package is already installed.
        
        Args:
            package_name: Name of package to check
            
        Returns:
            True if package is installed, False otherwise
        """
        try:
            # Extract package name without version constraints
            clean_name = package_name.split('>=')[0].split('==')[0].split('[')[0]
            result = subprocess.run([sys.executable, "-m", "pip", "show", clean_name], 
                                  capture_output=True, text=True)
            return result.returncode == 0
        except:
            return False
    
    def run_pip_command(self, command: List[str], package_name: str = "") -> InstallationResult:
        """
        Run a pip command and return the result.
        
        Args:
            command: List of command arguments
            package_name: Name of package being installed (for logging)
            
        Returns:
            InstallationResult with success status and any error messages
        """
        # Check if package is pre-installed in Colab
        if self.colab_mode and package_name:
            clean_name = package_name.split('>=')[0].split('==')[0].split('[')[0]
            if clean_name in self.colab_preinstalled and self.check_package_installed(package_name):
                print(f"⏭️  {package_name} - Already installed in Colab")
                return InstallationResult(success=True, package=package_name, 
                                        warning_message="Pre-installed in Colab")
        
        if self.dry_run:
            print(f"[DRY RUN] Would run: {' '.join(command)}")
            return InstallationResult(success=True, package=package_name)
        
        try:
            # Use sys.executable to ensure we're using the same Python interpreter
            full_command = [sys.executable, "-m"] + command
            
            # Colab-specific optimizations
            if self.colab_mode:
                # Add quiet flag for cleaner output in Colab
                if "--quiet" not in command and not self.verbose:
                    command.append("--quiet")
                # Increase timeout for Colab's potentially slower network
                timeout = 600  # 10 minutes for Colab
            else:
                timeout = 300  # 5 minutes for local
            
            result = subprocess.run(
                full_command,
                capture_output=not self.verbose,
                text=True,
                check=True,
                timeout=timeout
            )
            
            print(f"✅ {package_name or command[-1]}")
            return InstallationResult(success=True, package=package_name)
            
        except subprocess.TimeoutExpired:
            timeout_min = 10 if self.colab_mode else 5
            error_msg = f"Installation timeout ({timeout_min} minutes exceeded)"
            print(f"❌ {package_name or command[-1]} - {error_msg}")
            if self.colab_mode:
                print("   💡 Try restarting Colab runtime and running again")
            return InstallationResult(success=False, package=package_name, error_message=error_msg)
            
        except subprocess.CalledProcessError as e:
            error_msg = e.stderr.strip() if e.stderr else str(e)
            print(f"❌ {package_name or command[-1]} - Installation failed")
            if self.verbose and error_msg:
                print(f"   Error: {error_msg}")
            if self.colab_mode and "memory" in error_msg.lower():
                print("   💡 Try restarting Colab runtime to free memory")
            return InstallationResult(success=False, package=package_name, error_message=error_msg)
    
    def install_from_requirements(self) -> bool:
        """
        Install packages from requirements.txt if it exists.
        
        Returns:
            True if installation successful or file doesn't exist, False if failed
        """
        if not os.path.exists("requirements.txt"):
            print("ℹ️  requirements.txt not found - installing packages individually")
            return True
        
        print("\n📦 Installing from requirements.txt...")
        
        pip_args = ["pip", "install", "-r", "requirements.txt"]
        if self.upgrade:
            pip_args.append("--upgrade")
        
        result = self.run_pip_command(pip_args, "requirements.txt")
        self.results.append(result)
        
        return result.success
    
    def install_core_packages(self) -> bool:
        """
        Install core packages individually with Colab optimizations.
        
        Returns:
            True if all critical packages installed successfully
        """
        if self.colab_mode:
            print("\n📦 Installing core packages (Google Colab optimized)...")
            print("   💡 Some packages may already be pre-installed")
        else:
            print("\n📦 Installing core packages...")
        
        critical_failures = 0
        
        # Install packages in optimal order for Colab
        for package in self.core_packages:
            # Skip if pre-installed in Colab (unless upgrading)
            if self.colab_mode and not self.upgrade:
                clean_name = package.split('>=')[0].split('==')[0].split('[')[0]
                if clean_name in self.colab_preinstalled and self.check_package_installed(package):
                    print(f"⏭️  {package} - Using Colab pre-installed version")
                    continue
            
            pip_args = ["pip", "install", package]
            if self.upgrade:
                pip_args.append("--upgrade")
            
            # Colab-specific flags
            if self.colab_mode:
                pip_args.extend(["--no-cache-dir"])  # Save space in Colab
            
            result = self.run_pip_command(pip_args, package)
            self.results.append(result)
            
            if not result.success:
                # Consider yfinance, ta, and stable-baselines3 as critical for our system
                if any(critical in package.lower() for critical in ["yfinance", "stable-baselines3", "ta"]):
                    critical_failures += 1
        
        return critical_failures == 0
    
    def install_tensortrade(self) -> bool:
        """
        Install TensorTrade from GitHub repository with Colab optimizations.
        
        Returns:
            True if installation successful, False otherwise
        """
        if self.colab_mode:
            print("\n🎯 Installing TensorTrade from GitHub (Colab optimized)...")
            print("   ⏱️  This may take 5-10 minutes in Colab...")
            print("   💡 Colab may show warnings - these are usually safe to ignore")
        else:
            print("\n🎯 Installing TensorTrade from GitHub...")
            print("   This may take several minutes...")
        
        pip_args = ["pip", "install", self.tensortrade_url]
        if self.upgrade:
            pip_args.append("--upgrade")
        
        # Colab-specific optimizations for TensorTrade
        if self.colab_mode:
            pip_args.extend([
                "--no-cache-dir",  # Save space
                "--no-build-isolation",  # Faster builds in Colab
            ])
        
        result = self.run_pip_command(pip_args, "TensorTrade")
        self.results.append(result)
        
        if not result.success:
            print("\n⚠️  TensorTrade installation failed!")
            print("   This is a critical component for the RL Portfolio system.")
            if self.colab_mode:
                print("   💡 Colab troubleshooting steps:")
                print("   1. Restart runtime: Runtime → Restart runtime")
                print("   2. Try installing with: !pip install --no-deps git+https://github.com/tensortrade-org/tensortrade.git")
                print("   3. Check Colab's available disk space")
            else:
                print("   You can try installing manually:")
                print(f"   pip install {self.tensortrade_url}")
                print("   Or check the TensorTrade documentation for troubleshooting.")
        
        return result.success
    
    def install_platform_packages(self) -> bool:
        """
        Install platform-specific packages.
        
        Returns:
            True if installation successful or no platform packages needed
        """
        if not self.platform_packages:
            return True
        
        print(f"\n🖥️  Installing {platform.system()}-specific packages...")
        
        for package in self.platform_packages:
            pip_args = ["pip", "install", package]
            if self.upgrade:
                pip_args.append("--upgrade")
            
            result = self.run_pip_command(pip_args, package)
            self.results.append(result)
        
        return True  # Platform packages are not critical
    
    def verify_installation(self) -> Tuple[bool, List[str]]:
        """
        Verify that critical packages can be imported.
        
        Returns:
            Tuple of (success, list_of_missing_packages)
        """
        if self.dry_run:
            print("\n[DRY RUN] Skipping import verification")
            return True, []
        
        if self.colab_mode:
            print("\n🔍 Verifying installation (Google Colab)...")
        else:
            print("\n🔍 Verifying installation...")
        
        if self.colab_mode:
            # Colab-specific imports (some packages use different names)
            critical_imports = [
                ("pandas", "pandas"),
                ("numpy", "numpy"),
                ("yfinance", "yfinance"),
                ("ta", "ta"),
                ("stable_baselines3", "stable-baselines3"),
                ("gymnasium", "gymnasium"),  # Updated gym
                ("matplotlib", "matplotlib"),
                ("tensortrade", "TensorTrade")
            ]
        else:
            # Standard imports
            critical_imports = [
                ("pandas", "pandas"),
                ("numpy", "numpy"),
                ("yfinance", "yfinance"),
                ("ta", "ta"),
                ("stable_baselines3", "stable-baselines3"),
                ("gym", "gym"),
                ("matplotlib", "matplotlib"),
                ("tensortrade", "TensorTrade")
            ]
        
        missing_packages = []
        
        for import_name, package_name in critical_imports:
            try:
                __import__(import_name)
                if self.colab_mode and package_name in ["pandas", "numpy", "matplotlib"]:
                    print(f"✅ {package_name} - Using Colab pre-installed version")
                else:
                    print(f"✅ {package_name} - Import successful")
            except ImportError as e:
                print(f"❌ {package_name} - Import failed")
                if self.verbose:
                    print(f"   Error: {e}")
                missing_packages.append(package_name)
        
        # Colab-specific additional checks
        if self.colab_mode and len(missing_packages) == 0:
            print("\n💡 Colab Environment Check:")
            try:
                import torch
                device = "CPU" if not torch.cuda.is_available() else "GPU"
                print(f"   • PyTorch device: {device}")
            except ImportError:
                print("   • PyTorch: Not available (will be installed with stable-baselines3)")
            
            # Check available memory
            try:
                import psutil
                memory = psutil.virtual_memory()
                print(f"   • Available RAM: {memory.available / (1024**3):.1f} GB")
            except:
                print("   • Memory info: Not available")
        
        return len(missing_packages) == 0, missing_packages
    
    def print_summary(self) -> None:
        """Print installation summary."""
        print("\n" + "="*80)
        print("INSTALLATION SUMMARY")
        print("="*80)
        
        successful = sum(1 for r in self.results if r.success)
        failed = len(self.results) - successful
        
        print(f"✅ Successful installations: {successful}")
        if failed > 0:
            print(f"❌ Failed installations: {failed}")
            print("\nFailed packages:")
            for result in self.results:
                if not result.success:
                    print(f"   • {result.package}")
                    if result.error_message and self.verbose:
                        print(f"     Error: {result.error_message}")
        
        print("\n" + "="*80)
        print("NEXT STEPS")
        print("="*80)
        
        if failed == 0:
            print("🎉 All dependencies installed successfully!")
            if self.colab_mode:
                print("\n🚀 Ready to run in Google Colab!")
                print("   # Run the RL Portfolio system:")
                print("   !python main.py --help")
                print("   !python main.py --mode=training")
                print("   !python main.py --mode=evaluation")
                print("\n💡 Colab Tips:")
                print("   • Use '!' prefix for shell commands in Colab cells")
                print("   • Training may take 10-30 minutes on Colab CPU")
                print("   • Results will be saved to /content/ directory")
            else:
                print("\nYou can now run the RL Portfolio Rebalancing System:")
                print("   python main.py --help")
                print("   python main.py --mode=training")
                print("   python main.py --mode=evaluation")
        else:
            print("⚠️  Some packages failed to install.")
            print("   Please review the errors above and install missing packages manually.")
            if self.colab_mode:
                print("   Critical packages for Colab: yfinance, ta, stable-baselines3, TensorTrade")
                print("\n💡 Colab troubleshooting:")
                print("   • Try restarting runtime: Runtime → Restart runtime")
                print("   • Check available disk space: !df -h")
                print("   • Install failed packages manually: !pip install <package_name>")
            else:
                print("   Critical packages: pandas, numpy, yfinance, stable-baselines3, TensorTrade")
        
        print("\nFor more information:")
        if self.colab_mode:
            print("   • Upload config.py and main.py to your Colab session")
            print("   • Check Colab's /content/ directory for outputs")
        else:
            print("   • Check docs/ directory for detailed documentation")
            print("   • Review requirements.txt for package versions")
        print("   • Run with --verbose flag for detailed error messages")
    
    def run_installation(self) -> int:
        """
        Run the complete installation process.
        
        Returns:
            Exit code (0 for success, non-zero for failure)
        """
        print("="*80)
        print("RL PORTFOLIO REBALANCING SYSTEM - DEPENDENCY INSTALLER")
        if self.colab_mode:
            print("🚀 GOOGLE COLAB OPTIMIZED VERSION")
        print("="*80)
        print(f"Platform: {platform.system()} {platform.release()}")
        print(f"Python: {sys.version}")
        
        if self.colab_mode:
            print("🔧 Google Colab Environment Detected")
            print("   • CPU-optimized packages will be installed")
            print("   • Pre-installed packages will be detected and skipped")
            print("   • Memory-efficient installation order")
        
        if self.dry_run:
            print("🔍 DRY RUN MODE - No packages will be actually installed")
        if self.upgrade:
            print("⬆️  UPGRADE MODE - Existing packages will be upgraded")
        if self.verbose:
            print("📝 VERBOSE MODE - Detailed output enabled")
        
        print("="*80)
        
        # Step 1: Check Python version
        if not self.check_python_version():
            return 2
        
        # Step 2: Check pip availability
        if not self.check_pip_availability():
            return 3
        
        # Step 3: Install from requirements.txt (if exists)
        if not self.install_from_requirements():
            # Continue with individual package installation
            pass
        
        # Step 4: Install core packages
        if not self.install_core_packages():
            print("\n❌ Critical package installation failed!")
            self.print_summary()
            return 4
        
        # Step 5: Install TensorTrade
        if not self.install_tensortrade():
            print("\n❌ TensorTrade installation failed!")
            self.print_summary()
            return 5
        
        # Step 6: Install platform-specific packages
        self.install_platform_packages()
        
        # Step 7: Verify installation
        verification_success, missing_packages = self.verify_installation()
        
        # Step 8: Print summary
        self.print_summary()
        
        if not verification_success:
            print(f"\n❌ Verification failed - missing packages: {', '.join(missing_packages)}")
            return 1
        
        return 0

def parse_arguments() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Install dependencies for RL Portfolio Rebalancing System (Google Colab Optimized)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
EXAMPLES:
    python install_dependencies.py                    # Standard installation (auto-detects Colab)
    python install_dependencies.py --colab            # Force Colab optimizations
    python install_dependencies.py --upgrade          # Upgrade existing packages
    python install_dependencies.py --verbose          # Show detailed output
    python install_dependencies.py --dry-run          # Simulate installation

GOOGLE COLAB USAGE:
    !python install_dependencies.py                   # Run in Colab cell
    !python install_dependencies.py --verbose         # Detailed Colab output

EXIT CODES:
    0 - Success
    1 - General error
    2 - Python version incompatible  
    3 - pip not available
    4 - Critical package installation failed
    5 - TensorTrade installation failed
        """
    )
    
    parser.add_argument(
        "--upgrade", 
        action="store_true",
        help="Upgrade existing packages to latest versions"
    )
    
    parser.add_argument(
        "--verbose", 
        action="store_true",
        help="Show detailed installation output"
    )
    
    parser.add_argument(
        "--dry-run", 
        action="store_true",
        help="Show what would be installed without actually installing"
    )
    
    parser.add_argument(
        "--colab", 
        action="store_true",
        help="Force Google Colab optimizations (auto-detected by default)"
    )
    
    return parser.parse_args()

def main() -> int:
    """Main installation function."""
    try:
        args = parse_arguments()
        
        installer = DependencyInstaller(
            upgrade=args.upgrade,
            verbose=args.verbose,
            dry_run=args.dry_run,
            colab_mode=args.colab
        )
        
        return installer.run_installation()
        
    except KeyboardInterrupt:
        print("\n\n❌ Installation cancelled by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error during installation: {e}")
        try:
            if args.verbose:
                import traceback
                traceback.print_exc()
        except:
            import traceback
            traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())